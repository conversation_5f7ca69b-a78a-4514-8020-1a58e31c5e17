<div class="flex items-center justify-center min-h-screen">
    <div class="relative w-full max-w-2xl px-4">
        <form [formGroup]="searchForm" class="w-full">
            <div class="relative flex items-center w-full"> <!-- Search icon -->
                <div class="absolute left-3 text-gray-400"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5"
                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </div>
                <!-- Search input --> <input type="text" formControlName="search"
                    class="w-full py-3 pl-10 pr-12 rounded-full border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Search...">
                    <button type="submit">Search</button>
                <!-- Clear button (shows when there's text) --> <button *ngIf="search.value" type="button"
                    (click)="search.setValue('')" class="absolute right-3 text-gray-400 hover:text-gray-600"> <svg
                        xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </form>
    </div>
</div>
